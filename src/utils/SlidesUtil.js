export const getTargetSlide = (content, cursorLine) => {
  var lines = content.split('\n');

  var line = "";
  var slide = 0;
  var subSlide = 0;
  var page = 0;
  let prevSeperatorAt = -1;

  for (let i = 0; i < cursorLine; i++) {
    line = lines[i];

    if (/^--- *$/.test(line)) {
      prevSeperatorAt = i;
      slide = slide + 1;
      subSlide = 0;
      page++;
    } else if (/^-- *$/.test(line)) {
      prevSeperatorAt = i;
      subSlide = subSlide + 1;
      page++;
    }
  }

  const pages = content.split(/\n---? *\n/);

  var slideNumber = {
    "h": slide,
    "v": subSlide,
    "currentPage": pages[page] || '',
    "currentPageToCurrentLine": {
      content: lines.slice(prevSeperatorAt + 1, cursorLine).join('\n'),
      start: prevSeperatorAt === -1 ? 0 : lines.slice(0, prevSeperatorAt + 1).join('\n').length + 1,
      end: lines.slice(0, cursorLine).join('\n').length,
    },
    lines,
    "totalPages": {
      h: (content.match(/\n--- *\n/g) || []).length,
      v: (content.match(/\n-- *\n/g) || []).length
    },
    hasNotes: /\nNotes?:/.test(content)
  };

  return slideNumber;
};

export const  sendMessageToPreview = (server_host, method, params, args) => {
  document.getElementById('slides-frame').contentWindow.postMessage(JSON.stringify({
    method,
    params,
    args
  }), server_host);
};