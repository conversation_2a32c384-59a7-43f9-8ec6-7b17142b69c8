import { upsertDoc } from "@/actions/ticketAction";
import { AI_ASSISTANT_DIALOG, TEXT_SELECTION, TITLE_SELECTION } from "@/constants/actionTypes";
import { parseMarkdownTable } from "@/utils/SlateMarkdown";
import { exitBreak } from "@udecode/plate-break";
import { getNode, insertBreak, insertNodes, removeNodes, selectEditor, setSelection, useEditorRef } from "@udecode/plate-common";
import { deserializeMd } from "@udecode/plate-serializer-md";
import { cloneDeep } from "lodash";
import { useCallback, useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Editor as SlateEditor, Transforms } from 'slate';
import { ReactEditor } from "slate-react";

export const AIModalDataHandler = () => {
    const aiState = useSelector(state => state.uiState.aiDialog) || {};
    const dispatch = useDispatch();
    const editor = useEditorRef();
    const selection = useSelector(state => state.uiState.selection);

    useEffect(() => {
        if (!editor) {
            return;
        }

        const { response, trigger, doing, operation, usingTitle, visible, caller, path } = aiState;
        if (visible || caller != 'plate' || !response) {
            if (!visible) {
                dispatch({
                    type: TEXT_SELECTION,
                    value: null
                });
                dispatch({
                    type: TITLE_SELECTION,
                    value: false
                });

                ReactEditor.focus(editor);
            }
            return;
        }

        if (trigger === 'blockHandler') {
            setSelection(editor, {
                anchor: SlateEditor.start(editor, path || [editor.children.length - 1]),
                focus: SlateEditor.end(editor, path || [editor.children.length - 1]),
            })
            selectEditor(editor, { at: path || [editor.children.length - 1] });
        }

        if (operation === 'insertBelow' || trigger == 'entrance') {
            insertBelow(doing, response, trigger);
        } else if (operation === 'replace') {
            replaceSelection(doing, response, trigger, usingTitle);
        }

        dispatch({
            type: AI_ASSISTANT_DIALOG,
            value: {
                visible: false
            }
        });

        dispatch({
            type: TEXT_SELECTION,
            value: null
        });
        dispatch({
            type: TITLE_SELECTION,
            value: false
        });

        // ReactEditor.focus(editor);
    }, [aiState])

    const mdToSlate = (doing, content) => {
        let nodes = deserializeMd(editor, content);

        nodes = nodes.map(node => {
            if (node.type === 'p') {
                const table = parseMarkdownTable(node.children[0].text);
                console.log('table......', table)
                if (!table) return node;

                const children = [];
                if (table.headers) {
                    children.push({
                        type: 'tr',
                        children: table.headers.map(header => {
                            return {
                                type: 'th',
                                children: [{
                                    type: 'p',
                                    children: [{
                                        text: header
                                    }]
                                }]
                            }
                        })
                    });
                }

                if (table.rows) {
                    table.rows.forEach(row => {
                        children.push({
                            type: 'tr',
                            children: row.map(col => {
                                return {
                                    type: 'td',
                                    children: [{
                                        type: 'p',
                                        children: [{
                                            text: col
                                        }]
                                    }]
                                }
                            })
                        })
                    })
                }

                node.type = 'table';
                node.children = children;
                return node;
            }

            return node;
        });

        if (doing?.action === 'todos') {
            let listType = 'ul';
            for (let n of nodes) {
                if (['ul', 'ol'].includes(n.type)) {
                    listType = n.type;
                    break;
                }
            }
            nodes = nodes.map(node => {
                if (node.type === listType) {
                    return node.children.map(child => {
                        child.type = 'action_item'
                        if (!child.children) {
                            return null;
                        }
                        if (child.children[0]?.type != 'text') {
                            child.children = child.children[0].children;
                        }
                        return child;
                    })
                }

                return node;
            }).flat().filter(n => !!n);
        }

        return nodes;
    }

    const insertBelow = (doing, response, trigger) => {
        // const newNodes = mdToSlate(doing, (aiResponse || item)?.content || '');
        const newNodes = mdToSlate(doing, response?.content || '');

        if (trigger === 'ballonToolbar') {
            let focus = cloneDeep(editor.selection.focus);
            if (editor.selection.focus.path[0] < editor.selection.anchor.path[0]) {
                focus = cloneDeep(editor.selection.anchor);
            } else if (editor.selection.focus.path[0] === editor.selection.anchor.path[0]) {
                if (editor.selection.focus.offset < editor.selection.anchor.offset) {
                    focus = cloneDeep(editor.selection.anchor);
                }
            }

            setSelection(editor, { focus, anchor: focus });
        }

        try {
            exitBreak(editor, true);
        } catch (err) {
            console.error(err);
        }

        let insertAnchor = cloneDeep(editor.selection)
        if (!insertAnchor) {
            insertAnchor = {
                anchor: {
                    offset: 0,
                    path: [0, 0]
                },
                focus: {
                    offset: 0,
                    path: [0, 0]
                }
            }
        }

        insertNodes(editor, newNodes);
        setSelection(editor, insertAnchor);
        removeNodes(editor);

        setTimeout(() => {
            setSelectionToGeneratedContent(newNodes, insertAnchor.anchor.path);
        }, 300);
    }

    const replaceDocContent = (doing, content, trigger) => {
        if (!content || !selection) {
            return;
        }

        const newNodes = mdToSlate(doing, content);

        if (['blockHandler', 'cmd'].includes(trigger)) {
            let steps = selection.focus.path[0] - selection.anchor.path[0] + 1;
            for (let i = 0; i < steps; i++) {
                removeNodes(editor, { at: selection.anchor.path })
            }

            insertNodes(editor, newNodes, { at: selection.anchor.path });
            setTimeout(() => {
                setSelectionToGeneratedContent(newNodes, selection.anchor.path);
            }, 300);
        } else if (trigger === 'ballonToolbar') {
            Transforms.delete(editor, editor.selection);

            let anchor

            let node = getNode(editor, editor.selection.anchor.path)
            if (!!node?.text) {
                insertBreak(editor);
                anchor = cloneDeep(editor.selection.anchor);
            } else {
                anchor = cloneDeep(editor.selection.anchor);
                removeNodes(editor, { at: editor.selection.anchor.path })
            }

            insertNodes(editor, newNodes);
            setTimeout(() => {
                setSelectionToGeneratedContent(newNodes, anchor.path);
            }, 300);
        }
    }

    const replaceSelection = (doing, response, trigger, usingTitle) => {
        let content = response.content;

        if (!!editor.id && (doing.action === 'title' || usingTitle)) {
            const lines = content?.trim()?.split('\n') || [];

            let title = lines[0]?.replace(/^["“”]+|["“”]+$/g, '') || '';
            if (doing.action !== 'title' && !title.startsWith('# ')) {
                return replaceDocContent(doing, content, trigger);
            }

            title = title.replace(/^#* /, '');

            if (lines.length > 1) {
                content = lines.slice(1).join('\n')?.trim();
            } else {
                content = null;
            }

            if (!title) {
                if (doing.action === 'title') {
                    return;
                } else {
                    return replaceDocContent(doing, content, trigger);
                }
            }

            dispatch(upsertDoc({ data: { doc: { title, hid: editor.id } } }, (updatedDoc) => {
                if (doing.action !== 'title') {
                    replaceDocContent(doing, content, trigger);
                }

                // handleClose(true);
            }))
        } else {
            replaceDocContent(doing, content, trigger);
        }

    }

    const getStartLeaf = useCallback((node) => {
        const { children } = node;

        let startLeaf = {
            path: [0],
        }
        if (children[0].text) {
            startLeaf.text = children[0].text;

        } else {
            let childStartLeaf = getStartLeaf(children[0]);

            startLeaf.path = startLeaf.path.concat(childStartLeaf.path)
            startLeaf.text = childStartLeaf.text
        }

        return startLeaf;
    }, []);

    const getEndLeaf = useCallback((node) => {
        const { children } = node;

        const lastChildIndex = children.length - 1;

        let startLeaf = {
            path: [lastChildIndex],
        }
        if (children[lastChildIndex].text) {
            startLeaf.text = children[lastChildIndex].text;

        } else {
            let childStartLeaf = getStartLeaf(children[lastChildIndex]);

            startLeaf.path = startLeaf.path.concat(childStartLeaf.path)
            startLeaf.text = childStartLeaf.text
        }

        return startLeaf;
    }, []);

    const setSelectionToGeneratedContent = (newNodes, startPath) => {
        let startLeaf = getStartLeaf(newNodes[0]);
        let endLeaf = getEndLeaf(newNodes[newNodes.length - 1]);

        // console.log('anchor..............', startPath, startLeaf, endLeaf)


        let parentPath = startPath.length > 1 ? startPath.slice(0, -1) : cloneDeep(startPath);

        let newSelection = {
            anchor: {
                path: [parentPath].concat(startLeaf.path),
                offset: 0
            },
            focus: {
                path: parentPath.slice(0, -1).concat([parentPath[parentPath.length - 1] + newNodes.length - 1].concat(endLeaf.path)),
                offset: endLeaf.text.length
            }
        }

        try {
            setSelection(editor, newSelection)

        } catch (err) {
            console.error('err in setting selection............', err);
        }
    }

    return null;
}